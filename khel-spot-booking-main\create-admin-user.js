import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://iymmfpbcawwzxloaovmm.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml5bW1mcGJjYXd3enhsb2Fvdm1tIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU5ODI3NSwiZXhwIjoyMDY0MTc0Mjc1fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"; // This is a placeholder - we need the real service role key

const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  console.log('Creating admin user...');
  
  try {
    // First, delete any existing admin user
    const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(
      '223e4567-e89b-12d3-a456-426614174000'
    );
    
    if (deleteError && deleteError.message !== 'User not found') {
      console.log('Delete error (might be expected):', deleteError.message);
    }

    // Create the admin user
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email: '<EMAIL>',
      password: '789456',
      email_confirm: true,
      user_metadata: {
        full_name: 'Admin User'
      }
    });

    if (error) {
      console.error('Error creating admin user:', error);
      return false;
    }

    console.log('Admin user created successfully!');
    console.log('User ID:', data.user.id);
    console.log('Email:', data.user.email);
    
    // Update the user in the public.users table to set is_admin = true
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .upsert({
        id: data.user.id,
        email: data.user.email,
        full_name: 'Admin User',
        is_admin: true,
        is_active: true
      });
    
    if (updateError) {
      console.error('Error updating user profile:', updateError);
    } else {
      console.log('User profile updated with admin privileges');
    }
    
    return true;
  } catch (err) {
    console.error('Unexpected error:', err);
    return false;
  }
}

createAdminUser().then(success => {
  console.log('Result:', success ? 'SUCCESS' : 'FAILED');
  process.exit(success ? 0 : 1);
});
