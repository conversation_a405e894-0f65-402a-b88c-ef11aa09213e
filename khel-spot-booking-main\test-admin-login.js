import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://iymmfpbcawwzxloaovmm.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml5bW1mcGJjYXd3enhsb2Fvdm1tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTgyNzUsImV4cCI6MjA2NDE3NDI3NX0.nwhd7PPfGz9m5MCyah0RcLc_7XXnpEg47uPRjLyKi2Y";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testAdminLogin() {
  console.log('Testing admin login...');

  // First test: try to sign up the admin user
  console.log('Creating admin user via signup...');
  const { data: signupData, error: signupError } = await supabase.auth.signUp({
    email: '<EMAIL>',
    password: '789456',
    options: {
      data: {
        full_name: 'Admin User'
      }
    }
  });

  if (signupError) {
    console.error('Admin signup failed:', signupError);
  } else {
    console.log('Admin signup successful:', signupData.user?.email);
    console.log('Admin user ID:', signupData.user?.id);
  }

  // Test signin with the test user first
  console.log('Testing signin with test user...');
  const { data: testSigninData, error: testSigninError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'testpassword123',
  });

  if (testSigninError) {
    console.error('Test user signin failed:', testSigninError);
  } else {
    console.log('Test user signin successful:', testSigninData.user?.email);
  }

  // Now test admin login
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: '789456',
    });

    if (error) {
      console.error('Login error:', error);
      return false;
    }

    console.log('Login successful!');
    console.log('User:', data.user);
    console.log('Session:', data.session);
    
    // Test querying the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();
    
    if (userError) {
      console.error('User query error:', userError);
    } else {
      console.log('User data:', userData);
    }
    
    return true;
  } catch (err) {
    console.error('Unexpected error:', err);
    return false;
  }
}

testAdminLogin().then(success => {
  console.log('Test result:', success ? 'SUCCESS' : 'FAILED');
  process.exit(success ? 0 : 1);
});
